frappe.ui.form.on('POS Shift', {
    refresh: function(frm) {
        // Set cashier field filters based on POS Profile
        setCashierQuery(frm);

        // Auto-fill on new POS Shift
        if (frm.is_new()) {
            autoFillPOSShift(frm);
        }
    },

    pos_profile: function(frm) {
        // Reset fields when POS Profile changes
        frm.set_value('cashier', '');
        frm.set_value('previous_cashier', '');
        setCashierQuery(frm);
    }
});

function setCashierQuery(frm) {
    if (frm.doc.pos_profile) {
        frappe.db.get_doc('POS Profile', frm.doc.pos_profile)
            .then(doc => {
                console.log('POS Profile loaded:', doc);
                
                if (doc.applicable_for_users && doc.applicable_for_users.length > 0) {
                    let user_list = doc.applicable_for_users.map(u => u.user);
                    console.log('Applicable users:', user_list);

                    frm.set_query('cashier', () => ({
                        filters: {
                            enabled: 1,
                            name: ['in', user_list]
                        }
                    }));

                    frm.set_query('previous_cashier', () => ({
                        filters: {
                            enabled: 1,
                            name: ['in', user_list]
                        }
                    }));
                } else {
                    console.log('No applicable users found in POS Profile');
                    // If no users specified, show all enabled users
                    frm.set_query('cashier', () => ({
                        filters: { enabled: 1 }
                    }));
                    frm.set_query('previous_cashier', () => ({
                        filters: { enabled: 1 }
                    }));
                }
            })
            .catch(err => {
                console.error('Failed to fetch POS Profile:', err);
                // Fallback to show all enabled users
                frm.set_query('cashier', () => ({ filters: { enabled: 1 } }));
                frm.set_query('previous_cashier', () => ({ filters: { enabled: 1 } }));
            });
    } else {
        // No POS Profile selected, show all enabled users
        frm.set_query('cashier', () => ({ filters: { enabled: 1 } }));
        frm.set_query('previous_cashier', () => ({ filters: { enabled: 1 } }));
    }
}

function autoFillPOSShift(frm) {
    const current_user = frappe.session.user;
    console.log('Current user:', current_user);
    
    // Method 1: Use search_widget (more reliable)
    frappe.call({
        method: 'frappe.desk.search.search_widget',
        args: {
            doctype: 'POS Profile',
            txt: '',
            filters: [['POS Profile', 'disabled', '=', 0]]
        },
        callback: function(r) {
            console.log('Search widget response:', r);
            if (r.results && r.results.length > 0) {
                const profileNames = r.results.map(item => item.value);
                console.log('Found POS Profiles:', profileNames);
                checkProfilesForUser(frm, profileNames, current_user, 0);
            } else {
                console.log('No POS Profiles found via search widget');
                // Try alternative method
                tryDirectQuery(frm, current_user);
            }
        },
        error: function(err) {
            console.error('Search widget failed:', err);
            tryDirectQuery(frm, current_user);
        }
    });
}

// Alternative method using direct SQL query
function tryDirectQuery(frm, current_user) {
    console.log('Trying direct query method...');
    
    frappe.call({
        method: 'frappe.client.get_value',
        args: {
            doctype: 'POS Profile',
            fieldname: 'name',
            filters: { disabled: 0 }
        },
        callback: function(r) {
            console.log('Direct query response:', r);
            if (r.message && r.message.name) {
                // Found at least one profile, try to get all manually
                tryManualProfileCheck(frm, current_user);
            } else {
                console.log('No POS Profiles found');
            }
        },
        error: function(err) {
            console.error('Direct query failed:', err);
            tryManualProfileCheck(frm, current_user);
        }
    });
}

// Manual method - try common POS Profile names or use a server-side method
function tryManualProfileCheck(frm, current_user) {
    console.log('Trying manual profile check...');
    
    // Create a server-side method call to handle this
    frappe.call({
        method: 'frappe.core.api.file.get_files_in_folder',
        args: {
            folder: 'Home'
        },
        callback: function(r) {
            // This is just to test if we can make server calls
            console.log('Server call test successful');
            
            // Try to get profiles using a different approach
            tryAlternativeProfileFetch(frm, current_user);
        },
        error: function(err) {
            console.error('Server call test failed:', err);
            
            // Last resort: try to set a default if user knows the profile name
            console.log('All methods failed. Please check POS Profile permissions.');
            frappe.msgprint({
                title: 'Auto-fill Failed',
                message: 'Could not automatically fill POS Profile. Please select manually.',
                indicator: 'orange'
            });
        }
    });
}

function tryAlternativeProfileFetch(frm, current_user) {
    // Use get_list with minimal fields
    frappe.call({
        method: 'frappe.client.get_list',
        args: {
            doctype: 'POS Profile',
            filters: {},  // Empty filters
            fields: ['name'],
            limit_page_length: 20
        },
        callback: function(r) {
            console.log('Alternative fetch response:', r);
            if (r.message && r.message.length > 0) {
                // Filter out disabled profiles manually
                const profileNames = r.message.map(p => p.name);
                checkProfilesForUser(frm, profileNames, current_user, 0);
            } else {
                console.log('No profiles found in alternative fetch');
            }
        },
        error: function(err) {
            console.error('Alternative fetch failed:', err);
            console.log('All automated methods failed. Manual selection required.');
        }
    });
}

function checkProfilesForUser(frm, profileNames, current_user, index) {
    console.log(`Checking profile ${index + 1}/${profileNames.length}: ${profileNames[index]}`);
    
    if (index >= profileNames.length) {
        console.log('Current user not authorized for any POS Profile');
        frappe.msgprint({
            title: 'No Authorized Profile',
            message: `User ${current_user} is not authorized for any POS Profile. Please contact your administrator.`,
            indicator: 'red'
        });
        return;
    }

    const profileName = profileNames[index];

    frappe.db.get_doc('POS Profile', profileName)
        .then(doc => {
            console.log(`Loaded profile ${profileName}:`, doc);
            
            // Check if profile is disabled
            if (doc.disabled) {
                console.log(`Profile ${profileName} is disabled, checking next...`);
                checkProfilesForUser(frm, profileNames, current_user, index + 1);
                return;
            }
            
            const users = doc.applicable_for_users ? 
                doc.applicable_for_users.map(u => u.user) : [];
            
            console.log(`Users for profile ${profileName}:`, users);

            if (users.length === 0) {
                console.log(`Profile ${profileName} has no applicable users, checking next...`);
                checkProfilesForUser(frm, profileNames, current_user, index + 1);
                return;
            }

            if (users.includes(current_user)) {
                console.log(`Found matching profile: ${profileName}`);
                
                // Set the values
                frm.set_value('pos_profile', doc.name);
                
                // Set branch if available
                if (doc.branch) {
                    frm.set_value('branch', doc.branch);
                    console.log('Set branch:', doc.branch);
                }
                
                // Set current user as cashier
                setTimeout(() => {
                    frm.set_value('cashier', current_user);
                    console.log('Set cashier:', current_user);
                    
                    // Refresh the form to update the queries
                    frm.refresh_field('cashier');
                    frm.refresh_field('previous_cashier');
                }, 500);
                
                frappe.msgprint({
                    title: 'Auto-filled Successfully',
                    message: `POS Shift auto-filled with profile: ${doc.name}`,
                    indicator: 'green'
                });
                
                return; // Stop checking other profiles
            } else {
                console.log(`User ${current_user} not in profile ${profileName}, checking next...`);
                checkProfilesForUser(frm, profileNames, current_user, index + 1);
            }
        })
        .catch(err => {
            console.error(`Failed to load POS Profile ${profileName}:`, err);
            // Continue with next profile even if this one fails
            checkProfilesForUser(frm, profileNames, current_user, index + 1);
        });
}